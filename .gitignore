# Deno KV 数据库文件
data/

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp

# 系统文件
.DS_Store
Thumbs.db

# IDE 配置文件
.vscode/
.idea/
*.swp
*.swo

# 环境配置文件
.env
.env.local
.env.production

# 部署配置文件（包含敏感信息）
.deployctl.json

# 测试输出
test-results/
coverage/

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar

# Node.js 相关（如果有）
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 运行时文件
*.pid
*.seed
*.pid.lock

# 缓存文件
.cache/
.deno/
